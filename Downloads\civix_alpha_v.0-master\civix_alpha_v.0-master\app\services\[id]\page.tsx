import { MainLayout } from "@/components/layout/main-layout";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PageTransition, FadeIn } from "@/components/page-transition";
import { notFound } from "next/navigation";
import Link from "next/link";

interface ServiceDetailProps {
  params: Promise<{
    id: string;
  }>;
}

// Mock data for development - same as in services page
const mockServices = [
  {
    _id: "1",
    title: "Home Cleaning Service",
    category: "Cleaning",
    description: "Professional home cleaning service with experienced staff. We provide comprehensive cleaning for all rooms including kitchen, bathrooms, and living areas.",
    price: 75.00,
    isActive: true,
  },
  {
    _id: "2",
    title: "Plumbing Repair",
    category: "Maintenance",
    description: "Expert plumbing services for leaks, installations, and repairs. Available 24/7 for emergency situations.",
    price: 120.00,
    isActive: true,
  },
  {
    _id: "3",
    title: "Electrical Installation",
    category: "Electrical",
    description: "Licensed electricians for all your electrical needs. From simple repairs to complete installations.",
    price: 150.00,
    isActive: true,
  },
];

// Loading component
function ServiceSkeleton() {
  return (
    <div className="container px-4 py-16 md:px-6 md:py-24">
      <div className="mb-6">
        <div className="h-9 w-40 rounded-md bg-muted animate-pulse"></div>
      </div>
      
      <div className="rounded-lg border bg-card overflow-hidden animate-pulse">
        <div className="p-6">
          <div className="h-10 w-1/3 rounded-md bg-muted mb-2"></div>
          <div className="h-6 w-1/4 rounded-md bg-muted"></div>
        </div>
        <div className="p-6">
          <div className="mb-8 rounded-lg bg-muted p-6 h-32"></div>
          <div className="mb-8 grid gap-4 md:grid-cols-3">
            <div className="rounded-lg bg-muted p-4 h-20"></div>
            <div className="rounded-lg bg-muted p-4 h-20"></div>
            <div className="rounded-lg bg-muted p-4 h-20"></div>
          </div>
        </div>
        <div className="p-6">
          <div className="h-10 w-40 rounded-md bg-muted"></div>
        </div>
      </div>
    </div>
  );
}

// Error component
function ServiceError({ error }: { error: Error }) {
  return (
    <div className="container flex min-h-[400px] flex-col items-center justify-center px-4 py-16 text-center md:px-6">
      <h2 className="mb-2 text-2xl font-bold">Error Loading Service</h2>
      <p className="mb-6 text-muted-foreground">{error.message || "Service not found or unavailable"}</p>
      <Button asChild>
        <Link href="/services">Back to Services</Link>
      </Button>
    </div>
  );
}

// Dynamic metadata generation
export async function generateMetadata({ params }: ServiceDetailProps) {
  const resolvedParams = await params;
  const service = mockServices.find(s => s._id === resolvedParams.id);

  if (!service) {
    return {
      title: "Service Not Found - Civix",
      description: "The requested service could not be found",
    };
  }

  return {
    title: `${service.title} - Civix Services`,
    description: service.description,
  };
}

// Service detail component
function ServiceDetail({ id }: { id: string }) {
  const service = mockServices.find(s => s._id === id);

  if (!service) {
    notFound();
  }
    
    return (
      <div className="container px-4 py-16 md:px-6 md:py-24">
        <div className="mb-6">
          <Link href="/services">
            <Button variant="ghost" size="sm">
              ← Back to Services
            </Button>
          </Link>
        </div>
        
        <Card className="overflow-hidden">
          <CardHeader>
            <FadeIn>
              <CardTitle className="text-3xl">{service.title}</CardTitle>
              <CardDescription className="text-lg">
                {service.category}
              </CardDescription>
            </FadeIn>
          </CardHeader>
          <CardContent>
            <FadeIn delay={0.1}>
              <div className="mb-8 rounded-lg bg-muted p-6">
                <h2 className="mb-2 text-xl font-semibold">Description</h2>
                <p className="text-muted-foreground">{service.description}</p>
              </div>
              
              <div className="mb-8 grid gap-4 md:grid-cols-3">
                <div className="rounded-lg bg-muted p-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Price</h3>
                  <p className="mt-1 text-xl font-semibold">${service.price.toFixed(2)}</p>
                </div>
                
                <div className="rounded-lg bg-muted p-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Category</h3>
                  <p className="mt-1 text-xl font-semibold">{service.category}</p>
                </div>
                
                <div className="rounded-lg bg-muted p-4">
                  <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
                  <p className="mt-1 text-xl font-semibold">
                    {service.isActive ? "Active" : "Inactive"}
                  </p>
                </div>
              </div>
            </FadeIn>
          </CardContent>
          <CardFooter>
            <FadeIn delay={0.2}>
              <Link href={`/user/bookings/new/${service._id}`}>
                <Button size="lg">Book This Service</Button>
              </Link>
            </FadeIn>
          </CardFooter>
        </Card>
      </div>
    );
}

// Page component
export default async function ServicePage({ params }: ServiceDetailProps) {
  const resolvedParams = await params;

  return (
    <MainLayout>
      <PageTransition>
        <ServiceDetail id={resolvedParams.id} />
      </PageTransition>
    </MainLayout>
  );
}
