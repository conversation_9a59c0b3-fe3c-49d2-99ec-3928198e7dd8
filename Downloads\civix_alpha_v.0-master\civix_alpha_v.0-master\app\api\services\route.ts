import { NextRequest } from "next/server";
import { z } from "zod";
import connectDB from "@/lib/db";
import Service from "@/models/Service";
import { createApiResponse, requireAuth, handleApiError } from "@/lib/api-utils";

// Schema for query parameters
const QuerySchema = z.object({
  page: z.coerce.number().int().positive().optional().default(1),
  limit: z.coerce.number().int().positive().optional().default(10),
  category: z.string().optional(),
  agentId: z.string().optional(),
  isActive: z.enum(["true", "false"]).optional(),
  search: z.string().optional(),
  sort: z.string().optional().default("createdAt"),
  order: z.enum(["asc", "desc"]).optional().default("desc"),
});

// Mock data for development when MongoDB is not available
const mockServices = [
  {
    _id: "1",
    title: "Home Cleaning Service",
    category: "Cleaning",
    description: "Professional home cleaning service with experienced staff. We provide comprehensive cleaning for all rooms including kitchen, bathrooms, and living areas.",
    price: 75.00,
    isActive: true,
    createdAt: new Date("2024-01-15"),
  },
  {
    _id: "2",
    title: "Plumbing Repair",
    category: "Maintenance",
    description: "Expert plumbing services for leaks, installations, and repairs. Available 24/7 for emergency situations.",
    price: 120.00,
    isActive: true,
    createdAt: new Date("2024-01-14"),
  },
  {
    _id: "3",
    title: "Electrical Installation",
    category: "Electrical",
    description: "Licensed electricians for all your electrical needs. From simple repairs to complete installations.",
    price: 150.00,
    isActive: true,
    createdAt: new Date("2024-01-13"),
  },
  {
    _id: "4",
    title: "Garden Maintenance",
    category: "Landscaping",
    description: "Complete garden care including lawn mowing, pruning, and seasonal maintenance.",
    price: 90.00,
    isActive: true,
    createdAt: new Date("2024-01-12"),
  },
  {
    _id: "5",
    title: "AC Repair & Maintenance",
    category: "HVAC",
    description: "Professional air conditioning repair and maintenance services. Keep your home cool and comfortable.",
    price: 110.00,
    isActive: true,
    createdAt: new Date("2024-01-11"),
  },
  {
    _id: "6",
    title: "Painting Service",
    category: "Home Improvement",
    description: "Interior and exterior painting services with high-quality materials and professional finish.",
    price: 200.00,
    isActive: true,
    createdAt: new Date("2024-01-10"),
  },
];

// GET handler for listing services
export async function GET(request: NextRequest) {
  try {
    // Try to connect to MongoDB first
    let useDatabase = true;
    try {
      await connectDB();
    } catch (dbError) {
      console.log("⚠️ MongoDB not available, using mock data for development");
      useDatabase = false;
    }

    // Parse query params for pagination/filtering
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const category = url.searchParams.get("category");
    const search = url.searchParams.get("query");
    const skip = (page - 1) * limit;

    let services, totalCount;

    if (useDatabase) {
      // Build query
      const query: Record<string, unknown> = {};
      if (category && category !== "all") query.category = category;
      if (search) {
        query.$or = [
          { title: { $regex: search, $options: "i" } },
          { description: { $regex: search, $options: "i" } },
          { category: { $regex: search, $options: "i" } },
        ];
      }

      // Fetch services and count from database
      [services, totalCount] = await Promise.all([
        Service.find(query).sort({ createdAt: -1 }).skip(skip).limit(limit).lean(),
        Service.countDocuments(query),
      ]);
    } else {
      // Use mock data
      let filteredServices = [...mockServices];

      // Apply category filter
      if (category && category !== "all") {
        filteredServices = filteredServices.filter(service =>
          service.category.toLowerCase() === category.toLowerCase()
        );
      }

      // Apply search filter
      if (search) {
        const searchLower = search.toLowerCase();
        filteredServices = filteredServices.filter(service =>
          service.title.toLowerCase().includes(searchLower) ||
          service.description.toLowerCase().includes(searchLower) ||
          service.category.toLowerCase().includes(searchLower)
        );
      }

      totalCount = filteredServices.length;
      services = filteredServices.slice(skip, skip + limit);
    }

    return createApiResponse(true, {
      data: services,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    return handleApiError(error);
  }
}

// POST handler for creating a new service
export async function POST(request: NextRequest) {
  try {
    // Check authentication and authorization
    const { session, error } = await requireAuth(request, ["AGENT", "ADMIN", "SUPER_ADMIN"]);
    if (error) return error;
    
    // Parse request body
    const body = await request.json();
    
    // Create schema for service creation
    const ServiceCreateSchema = z.object({
      title: z.string().min(3).max(100),
      description: z.string().min(10),
      price: z.number().positive(),
      category: z.string().min(2),
      isActive: z.boolean().optional().default(true),
    });
    
    // Validate service data
    const serviceData = ServiceCreateSchema.parse(body);
    
    // Connect to database
    await connectDB();
    
    // Add agent ID from session
    const newServiceData = {
      ...serviceData,
      agentId: session!.user.id,
    };
    
    // Create new service
    const newService = await Service.create(newServiceData);
    
    // Return created service
    const createdService = await Service.findById(newService._id)
      .populate("agentId", "name email");
    
    return createApiResponse(
      true,
      createdService,
      undefined,
      201
    );
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return createApiResponse(
        false,
        undefined,
        "Invalid service data",
        400
      );
    }
    
    return handleApiError(error);
  }
} 