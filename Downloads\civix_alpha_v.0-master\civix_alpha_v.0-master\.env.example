# Database Configuration - Cloud MongoDB
MONGODB_URI=mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=civix

# NextAuth Configuration 
AUTH_SECRET=your-auth-secret-here
NEXTAUTH_SECRET=your-nextauth-secret-here
NEXTAUTH_DEBUG=false

# Application URLs (Update with your Vercel domain)
NEXTAUTH_URL=https://your-app-name.vercel.app
NEXT_PUBLIC_APP_URL=https://your-app-name.vercel.app
NEXT_PUBLIC_SITE_URL=https://your-app-name.vercel.app

# API Configuration
NEXT_PUBLIC_API_URL=https://your-app-name.vercel.app/api
REACT_APP_API_URL=https://your-app-name.vercel.app/api

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Site Configuration
NEXT_PUBLIC_SITE_NAME=Civix
NEXT_PUBLIC_SITE_DESCRIPTION=Professional service booking platform
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+****************
