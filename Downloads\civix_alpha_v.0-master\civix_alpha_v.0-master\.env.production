# Production Environment Variables for Vercel

# Database Configuration - Cloud MongoDB
MONGODB_URI=mongodb+srv://machanva252:<EMAIL>/?retryWrites=true&w=majority&appName=civix

# NextAuth Configuration 
AUTH_SECRET=b1cfc457b388448d0e10d6ce33874ee82b69b0b916d997849aa41eaa4240c08d
NEXTAUTH_SECRET=56aa928b56b64992ee25ec9a438c056f57b79ce28f504db9a84e8dfbf34e34ac
NEXTAUTH_DEBUG=false

# Application URLs - UPDATE THESE WITH YOUR VERCEL DOMAIN
NEXTAUTH_URL=https://civix-app.vercel.app
NEXT_PUBLIC_APP_URL=https://civix-app.vercel.app
NEXT_PUBLIC_SITE_URL=https://civix-app.vercel.app

# API Configuration
NEXT_PUBLIC_API_URL=https://civix-app.vercel.app/api
REACT_APP_API_URL=https://civix-app.vercel.app/api

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=ghsr emqx kkkv wpxz

# Site Configuration
NEXT_PUBLIC_SITE_NAME=Civix
NEXT_PUBLIC_SITE_DESCRIPTION=Professional service booking platform
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+****************
