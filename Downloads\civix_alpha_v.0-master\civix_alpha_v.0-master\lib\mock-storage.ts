// Mock storage for development when MongoDB is not available
// In production, this would be replaced with actual database operations

export interface MockUser {
  id: string;
  name: string;
  email: string;
  password: string;
  role: 'USER' | 'AGENT' | 'ADMIN' | 'SUPER_ADMIN';
  phone?: string;
  isActive: boolean;
  createdAt: Date;
  lastLogin?: Date;
}

// Global in-memory storage for development that persists across modules
declare global {
  var mockUsers: MockUser[] | undefined;
}

// Use global storage to persist across different API routes
const mockUsers: MockUser[] = globalThis.mockUsers || [];
if (!globalThis.mockUsers) {
  globalThis.mockUsers = mockUsers;

  // Add some default users for development
  // Check if the session user exists, if not add them
  const sessionUserId = 'user_1754316936225_v8l70aloj';
  const sessionUserExists = mockUsers.find(u => u.id === sessionUserId);

  if (!sessionUserExists) {
    mockUsers.push({
      id: sessionUserId, // This was the ID from the session
      name: '<PERSON>',
      email: '<EMAIL>',
      password: '$2a$10$defaultHashedPassword', // This would be a real bcrypt hash
      role: 'USER',
      phone: '',
      isActive: true,
      createdAt: new Date(),
      lastLogin: new Date(),
    });
  }
}

export const mockStorage = {
  // User operations
  users: {
    findByEmail: (email: string): MockUser | undefined => {
      return mockUsers.find(user => user.email === email);
    },
    
    findById: (id: string): MockUser | undefined => {
      return mockUsers.find(user => user.id === id);
    },
    
    create: (userData: Omit<MockUser, 'id' | 'createdAt'>): MockUser => {
      const newUser: MockUser = {
        ...userData,
        id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        createdAt: new Date(),
      };
      mockUsers.push(newUser);
      return newUser;
    },
    
    updateLastLogin: (id: string): boolean => {
      const user = mockUsers.find(user => user.id === id);
      if (user) {
        user.lastLogin = new Date();
        return true;
      }
      return false;
    },
    
    getAll: (): MockUser[] => {
      return [...mockUsers]; // Return a copy to prevent direct modification
    },
    
    count: (): number => {
      return mockUsers.length;
    }
  }
};

// Export for debugging purposes
export const getMockStorageStats = () => {
  return {
    userCount: mockUsers.length,
    users: mockUsers.map(user => ({
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      createdAt: user.createdAt,
      lastLogin: user.lastLogin
    }))
  };
};
