import { MainLayout } from "@/components/layout/main-layout";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { PageTransition, FadeIn, StaggerChildren } from "@/components/page-transition";

export const metadata = {
  title: "Services | Civix",
  description: "Browse our available services",
};

// Mock data for development
const mockServices = [
  {
    _id: "1",
    title: "Home Cleaning Service",
    category: "Cleaning",
    description: "Professional home cleaning service with experienced staff.",
    price: 75.00,
  },
  {
    _id: "2", 
    title: "Plumbing Repair",
    category: "Maintenance",
    description: "Expert plumbing services for leaks, installations, and repairs.",
    price: 120.00,
  },
  {
    _id: "3",
    title: "Electrical Installation",
    category: "Electrical",
    description: "Licensed electricians for all your electrical needs.",
    price: 150.00,
  },
];

export default function Services() {
  return (
    <MainLayout>
      <PageTransition>
        <div className="container px-4 py-16 md:px-6 md:py-24">
          <div className="mb-12">
            <FadeIn>
              <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                Services
              </h1>
            </FadeIn>
            <FadeIn delay={0.1}>
              <p className="mt-4 text-lg text-muted-foreground">
                Discover our range of professional services
              </p>
            </FadeIn>
          </div>

          <StaggerChildren className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
            {mockServices.map((service) => (
              <Card key={service._id} className="flex flex-col">
                <CardHeader>
                  <CardTitle>{service.title}</CardTitle>
                  <CardDescription className="text-sm text-muted-foreground">
                    {service.category}
                  </CardDescription>
                </CardHeader>
                <CardContent className="flex-1">
                  <p className="text-sm text-muted-foreground line-clamp-3">
                    {service.description}
                  </p>
                  <p className="mt-4 text-2xl font-bold">
                    ${service.price.toFixed(2)}
                  </p>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Link href={`/services/${service._id}`}>
                    <Button>View Details</Button>
                  </Link>
                </CardFooter>
              </Card>
            ))}
          </StaggerChildren>
        </div>
      </PageTransition>
    </MainLayout>
  );
}
