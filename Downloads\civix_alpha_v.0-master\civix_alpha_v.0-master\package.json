{"name": "civix", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env PORT=80 next dev", "build": "next build", "start": "cross-env PORT=80 next start", "lint": "next lint", "setup:env": "node scripts/setup-env.js", "setup:local-mongodb": "node scripts/setup-local-mongodb.js", "test:mongodb": "node scripts/test-mongodb-connection.js", "verify:auth": "node scripts/verify-auth-config.js", "reset:password": "node scripts/reset-user-password.js", "seed:admins": "node scripts/seed-admin-users.js", "verify:admins": "node scripts/verify-admin-users.js", "seed:users": "node scripts/seed-users.js", "seed:all": "npm run seed:users", "clean:users": "node scripts/seed-users.js --clean", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:reload": "pm2 reload ecosystem.config.js --env production", "pm2:stop": "pm2 stop civix", "pm2:status": "pm2 status civix", "pm2:logs": "pm2 logs civix", "pm2:monitor": "pm2 monit civix", "deploy:prod": "pm2 deploy ecosystem.config.js production", "deploy:staging": "pm2 deploy ecosystem.config.js staging"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@shadcn/ui": "^0.0.4", "@tanstack/react-query": "^5.83.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "framer-motion": "^12.23.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.16.4", "next": "15.4.2", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "nookies": "^2.5.2", "react": "19.1.0", "react-day-picker": "^9.8.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5"}}