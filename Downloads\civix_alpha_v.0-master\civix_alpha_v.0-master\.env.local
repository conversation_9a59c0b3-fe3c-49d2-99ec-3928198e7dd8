# Database Configuration - Cloud MongoDB (working solution)
MONGODB_URI=mongodb+srv://machanva252:<EMAIL>/?retryWrites=true&w=majority&appName=civix

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SITE_URL=http://localhost:3000
AUTH_SECRET=z8xpnz9tzmlp793tmok8shf80tl4uzjgcda6lcq9p
NEXTAUTH_SECRET=z8xpnz9tzmlp793tmok8shf80tl4uzjgcda6lcq9p

# Email Configuration (Optional for local development)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password-here

# Debug Settings (Set to true for development)
NEXTAUTH_DEBUG=true

# Site Configuration
NEXT_PUBLIC_SITE_NAME=Civix
NEXT_PUBLIC_SITE_DESCRIPTION=Professional service booking platform
NEXT_PUBLIC_CONTACT_EMAIL=<EMAIL>
NEXT_PUBLIC_CONTACT_PHONE=+****************

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000/api
