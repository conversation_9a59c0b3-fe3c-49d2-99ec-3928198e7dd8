import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { createNamespaceLogger } from '@/lib/logger';
import connectDB from '@/lib/db';
import User from '@/models/User';

const logger = createNamespaceLogger('api:auth:register');

export async function POST(request: NextRequest) {
  try {
    logger.info('Processing registration request (using mock data for development)');

    // Skip database connection for development

    const { name, email, password, phone } = await request.json();

    // Validate input
    if (!name || !email || !password) {
      logger.warn('Registration attempt with missing required fields');
      return NextResponse.json(
        { success: false, error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    // Connect to MongoDB database
    await connectDB();
    logger.debug('Checking if email exists in database', { email });

    // Check if email is already in use
    const existingUser = await User.findOne({ email });

    if (existingUser) {
      logger.warn('Registration attempt with existing email', { email });
      return NextResponse.json(
        { success: false, error: 'Email is already in use' },
        { status: 400 }
      );
    }

    // Validate password strength
    if (password.length < 8) {
      logger.warn('Registration attempt with weak password', { email });
      return NextResponse.json(
        { success: false, error: 'Password must be at least 8 characters long' },
        { status: 400 }
      );
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create new user in database
    const newUser = new User({
      name,
      email,
      password: hashedPassword,
      role: 'USER',
      phone: phone || '',
      isActive: true,
    });

    await newUser.save();

    logger.info('User registered successfully in database', { userId: newUser._id.toString(), email });

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: 'User registered successfully',
        data: {
          id: newUser._id.toString(),
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    logger.error('Error during registration', { 
      error: error instanceof Error ? error.message : String(error)
    });
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
} 