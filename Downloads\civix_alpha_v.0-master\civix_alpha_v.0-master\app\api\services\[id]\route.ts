import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db';
import Service from '@/models/Service';
import mongoose from 'mongoose';
import { auth } from '@/lib/auth';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// Mock data for development when MongoDB is not available
const mockServices = [
  {
    _id: "1",
    title: "Home Cleaning Service",
    category: "Cleaning",
    description: "Professional home cleaning service with experienced staff. We provide comprehensive cleaning for all rooms including kitchen, bathrooms, and living areas.",
    price: 75.00,
    isActive: true,
    createdAt: new Date("2024-01-15"),
  },
  {
    _id: "2",
    title: "Plumbing Repair",
    category: "Maintenance",
    description: "Expert plumbing services for leaks, installations, and repairs. Available 24/7 for emergency situations.",
    price: 120.00,
    isActive: true,
    createdAt: new Date("2024-01-14"),
  },
  {
    _id: "3",
    title: "Electrical Installation",
    category: "Electrical",
    description: "Licensed electricians for all your electrical needs. From simple repairs to complete installations.",
    price: 150.00,
    isActive: true,
    createdAt: new Date("2024-01-13"),
  },
  {
    _id: "4",
    title: "Garden Maintenance",
    category: "Landscaping",
    description: "Complete garden care including lawn mowing, pruning, and seasonal maintenance.",
    price: 90.00,
    isActive: true,
    createdAt: new Date("2024-01-12"),
  },
  {
    _id: "5",
    title: "AC Repair & Maintenance",
    category: "HVAC",
    description: "Professional air conditioning repair and maintenance services. Keep your home cool and comfortable.",
    price: 110.00,
    isActive: true,
    createdAt: new Date("2024-01-11"),
  },
  {
    _id: "6",
    title: "Painting Service",
    category: "Home Improvement",
    description: "Interior and exterior painting services with high-quality materials and professional finish.",
    price: 200.00,
    isActive: true,
    createdAt: new Date("2024-01-10"),
  },
];

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Try to connect to MongoDB first
    let useDatabase = true;
    try {
      await connectDB();
    } catch (dbError) {
      console.log("⚠️ MongoDB not available, using mock data for development");
      useDatabase = false;
    }

    const resolvedParams = await params;
    const { id } = resolvedParams;

    let service;

    if (useDatabase) {
      // Validate MongoDB ID
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid service ID format',
          },
          { status: 400 }
        );
      }

      // Find service by ID from database
      service = await Service.findById(id).lean();
    } else {
      // Find service from mock data
      service = mockServices.find(s => s._id === id);
    }

    if (!service) {
      return NextResponse.json(
        {
          success: false,
          error: 'Service not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: service,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify authentication and authorization
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    await connectDB();
    
    const resolvedParams = await params;
    const { id } = resolvedParams;
    
    // Validate MongoDB ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid service ID format',
        },
        { status: 400 }
      );
    }
    
    // Find service by ID
    const service = await Service.findById(id);
    
    if (!service) {
      return NextResponse.json(
        {
          success: false,
          error: 'Service not found',
        },
        { status: 404 }
      );
    }
    
    // Check if user is authorized to update this service
    const isOwner = service.agentId.toString() === session.user.id;
    const isAdmin = ['ADMIN', 'SUPER_ADMIN'].includes(session.user.role);
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Not authorized to update this service' },
        { status: 403 }
      );
    }
    
    const body = await request.json();
    
    // Update service
    const updatedService = await Service.findByIdAndUpdate(
      id,
      { ...body, updatedAt: new Date() },
      { new: true }
    );
    
    return NextResponse.json({
      success: true,
      data: updatedService,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify authentication and authorization
    const session = await auth();
    if (!session || !session.user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    await connectDB();
    
    const resolvedParams = await params;
    const { id } = resolvedParams;
    
    // Validate MongoDB ID
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid service ID format',
        },
        { status: 400 }
      );
    }
    
    // Find service by ID
    const service = await Service.findById(id);
    
    if (!service) {
      return NextResponse.json(
        {
          success: false,
          error: 'Service not found',
        },
        { status: 404 }
      );
    }
    
    // Check if user is authorized to delete this service
    const isOwner = service.agentId.toString() === session.user.id;
    const isAdmin = ['ADMIN', 'SUPER_ADMIN'].includes(session.user.role);
    
    if (!isOwner && !isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Not authorized to delete this service' },
        { status: 403 }
      );
    }
    
    // Delete service (soft delete)
    await Service.findByIdAndUpdate(id, { isActive: false });
    
    return NextResponse.json({
      success: true,
      message: `Service ${id} deleted successfully`,
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
} 